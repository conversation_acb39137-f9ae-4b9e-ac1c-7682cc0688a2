import { siteConfig } from '@/lib/config'
import { loadExternalResource } from '@/lib/utils'
import { useEffect, useRef, useState } from 'react'

/**
 * 音乐播放器
 * @returns
 */
const Player = () => {
  const [player, setPlayer] = useState()
  const ref = useRef(null)
  const lrcType = JSON.parse(siteConfig('MUSIC_PLAYER_LRC_TYPE'))
  const playerVisible = JSON.parse(siteConfig('MUSIC_PLAYER_VISIBLE'))
  const autoPlay = JSON.parse(siteConfig('MUSIC_PLAYER_AUTO_PLAY'))
  const meting = JSON.parse(siteConfig('MUSIC_PLAYER_METING'))
  const order = siteConfig('MUSIC_PLAYER_ORDER')
  const audio = siteConfig('MUSIC_PLAYER_AUDIO_LIST')

  const musicPlayerEnable = siteConfig('MUSIC_PLAYER')
  const musicPlayerCDN = siteConfig('MUSIC_PLAYER_CDN_URL')
  const musicMetingEnable = siteConfig('MUSIC_PLAYER_METING')

  const initMusicPlayer = async () => {
    if (!musicPlayerEnable) {
      return
    }

    try {
      // 加载APlayer CSS
      await loadExternalResource('https://unpkg.com/aplayer@1.10.1/dist/APlayer.min.css', 'css')

      // 加载APlayer JS
      await loadExternalResource(musicPlayerCDN || 'https://unpkg.com/aplayer@1.10.1/dist/APlayer.min.js', 'js')

      if (musicMetingEnable) {
        try {
          await loadExternalResource('https://unpkg.com/meting@2.0.1/dist/Meting.min.js', 'js')
        } catch (error) {
          console.warn('MetingJS加载失败:', error)
        }
      }

      // 确保容器存在且APlayer已加载
      if (!meting && window.APlayer && ref.current) {
        setTimeout(() => {
          try {
            setPlayer(
              new window.APlayer({
                container: ref.current,
                fixed: true,
                lrcType: lrcType,
                autoplay: autoPlay,
                order: order,
                audio: audio
              })
            )
          } catch (error) {
            console.error('APlayer初始化失败:', error)
          }
        }, 100)
      }
    } catch (error) {
      console.error('播放器资源加载失败:', error)
    }
  }

  useEffect(() => {
    if (musicPlayerEnable) {
      initMusicPlayer()
    }

    return () => {
      if (player) {
        try {
          player.destroy()
        } catch (error) {
          console.warn('播放器销毁失败:', error)
        }
      }
      setPlayer(undefined)
    }
  }, [])

  return (
    <div className={playerVisible ? 'visible' : 'invisible'}>
      {meting ? (
        <meting-js
          fixed='true'
          type='playlist'
          preload='auto'
          api={siteConfig(
            'MUSIC_PLAYER_METING_API',
            'https://api.i-meto.com/meting/api?server=:server&type=:type&id=:id&r=:r'
          )}
          autoplay={autoPlay}
          order={siteConfig('MUSIC_PLAYER_ORDER')}
          server={siteConfig('MUSIC_PLAYER_METING_SERVER')}
          id={siteConfig('MUSIC_PLAYER_METING_ID')}
        />
      ) : (
        <div ref={ref} data-player={player} />
      )}
    </div>
  )
}

export default Player
